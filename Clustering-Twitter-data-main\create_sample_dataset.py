# 创建示例数据集的脚本
# Script to create a sample dataset for testing

import pandas as pd
import numpy as np
import random
from datetime import datetime, timedelta

def generate_sample_twitter_dataset(n_samples=50000):
    """生成示例Twitter数据集"""
    print(f"正在生成包含 {n_samples:,} 条记录的示例数据集...")
    
    # 设置随机种子以确保可重现性
    np.random.seed(42)
    random.seed(42)
    
    # 定义不同主题的推文模板
    positive_templates = [
        # 工作相关 - 正面
        "Love my new job! The team is amazing and the work is so fulfilling.",
        "Just got promoted! Hard work really pays off. Feeling grateful.",
        "Great meeting today with the team. We're making real progress on the project.",
        "Working from home today and feeling so productive. Best decision ever!",
        "Just finished a challenging project. The sense of accomplishment is incredible.",
        
        # 娱乐相关 - 正面  
        "Just watched an amazing movie! The cinematography was absolutely stunning.",
        "Concert last night was incredible! The energy was off the charts.",
        "Playing my favorite video game and having such a blast with friends.",
        "New album from my favorite artist just dropped. It's pure perfection!",
        "Weekend party was so much fun! Dancing until sunrise with great people.",
        
        # 情感相关 - 正面
        "Feeling so happy and grateful for all the wonderful people in my life.",
        "Love spending quality time with my family. These moments are precious.",
        "Excited about the future and all the possibilities ahead of me.",
        "Proud of how far I've come. Personal growth is such a beautiful journey.",
        "Blessed to have such amazing friends who always support me.",
        
        # 生活相关 - 正面
        "Beautiful sunny day! Perfect weather for a walk in the park.",
        "Cooked an amazing dinner tonight. Cooking is such a therapeutic activity.",
        "Morning coffee tastes extra good today. Starting the day right!",
        "Weekend plans are set! Looking forward to relaxing and recharging.",
        "Home sweet home. There's nothing like the comfort of your own space.",
        
        # 教育相关 - 正面
        "Aced my exam today! All that studying really paid off in the end.",
        "Learning so much in my online course. Knowledge is truly empowering.",
        "Graduation ceremony was beautiful! Proud of this major accomplishment.",
        "Research project is going well. Discovering fascinating new insights.",
        "University life is amazing! Meeting so many interesting people every day."
    ]
    
    negative_templates = [
        # 工作相关 - 负面
        "Another stressful day at the office. The workload is overwhelming.",
        "Job interview didn't go well. Feeling disappointed and discouraged.",
        "Deadline approaching fast and I'm nowhere near finished with this project.",
        "Office politics are getting really toxic. Need to find a new job.",
        "Working late again tonight. Work-life balance is non-existent here.",
        
        # 娱乐相关 - 负面
        "Movie was terrible! Wasted two hours of my life watching that.",
        "Concert was cancelled last minute. So disappointed and frustrated.",
        "Video game servers are down again. This is getting really annoying.",
        "New album is such a disappointment. What happened to good music?",
        "Party was boring and the music was awful. Left early.",
        
        # 情感相关 - 负面
        "Feeling really sad and lonely today. Everything seems overwhelming.",
        "Missing my family so much. Distance is really hard to handle.",
        "Worried about the future and all the uncertainty ahead of me.",
        "Disappointed in myself for not achieving my goals this year.",
        "Feeling stressed and anxious about everything going on right now.",
        
        # 生活相关 - 负面
        "Rainy weather is making me feel depressed and unmotivated.",
        "Burned dinner tonight. Cooking disasters seem to be my specialty.",
        "Coffee machine broke this morning. Day is ruined before it started.",
        "Weekend plans got cancelled. Now I'm stuck at home doing nothing.",
        "House is a mess and I don't have energy to clean it.",
        
        # 教育相关 - 负面
        "Failed my exam today. All that studying was apparently not enough.",
        "Online course is so confusing. The instructor explains nothing clearly.",
        "Graduation got postponed due to circumstances. So frustrated right now.",
        "Research project is a disaster. Nothing is working as expected.",
        "University stress is killing me. Too much pressure and competition."
    ]
    
    # 生成数据
    data = []
    
    for i in range(n_samples):
        # 随机选择情感标签 (0=负面, 4=正面)
        target = random.choice([0, 4])
        
        # 根据标签选择模板
        if target == 4:
            text = random.choice(positive_templates)
        else:
            text = random.choice(negative_templates)
        
        # 添加一些随机变化
        variations = [
            text,
            f"@user {text}",
            f"{text} #life",
            f"{text} http://example.com",
            f"RT @friend: {text}",
            f"{text}!!!",
            f"Just thinking... {text}",
            f"{text} 😊" if target == 4 else f"{text} 😢"
        ]
        
        final_text = random.choice(variations)
        
        # 生成其他字段
        tweet_id = 1467810000 + i
        date = "Mon Apr 06 22:19:45 PDT 2009"  # 固定日期格式
        flag = "NO_QUERY"
        user = f"user_{random.randint(1, 1000)}"
        
        data.append([target, tweet_id, date, flag, user, final_text])
    
    # 创建DataFrame
    df = pd.DataFrame(data, columns=['target', 'id', 'date', 'flag', 'user', 'text'])
    
    # 保存为CSV文件
    df.to_csv('twitter.csv', index=False, header=False, encoding='utf-8')
    
    print(f"✅ 示例数据集已生成！")
    print(f"📁 文件名: twitter.csv")
    print(f"📊 数据量: {len(df):,} 条记录")
    print(f"📈 正面情感: {len(df[df['target'] == 4]):,} 条")
    print(f"📉 负面情感: {len(df[df['target'] == 0]):,} 条")
    print(f"💾 文件大小: 约 {len(df) * 100 / 1024 / 1024:.1f} MB")
    
    # 显示前几行作为预览
    print(f"\n📋 数据预览:")
    print(df.head())
    
    return df

def main():
    """主函数"""
    print("Twitter数据集生成器")
    print("=" * 50)
    
    # 询问用户想要生成的数据量
    print("请选择要生成的数据集大小:")
    print("1. 小型数据集 (10,000 条记录) - 适合快速测试")
    print("2. 中型数据集 (50,000 条记录) - 适合完整分析")
    print("3. 大型数据集 (100,000 条记录) - 接近真实规模")
    print("4. 自定义大小")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        n_samples = 10000
    elif choice == "2":
        n_samples = 50000
    elif choice == "3":
        n_samples = 100000
    elif choice == "4":
        try:
            n_samples = int(input("请输入记录数量: "))
            if n_samples <= 0:
                print("❌ 数量必须大于0")
                return
        except ValueError:
            print("❌ 请输入有效的数字")
            return
    else:
        print("❌ 无效选择，使用默认大小 (50,000)")
        n_samples = 50000
    
    # 生成数据集
    df = generate_sample_twitter_dataset(n_samples)
    
    print(f"\n🎉 数据集生成完成！")
    print(f"现在您可以运行聚类分析脚本了:")
    print(f"  python Clustering_twitter.py")
    print(f"  python run_analysis.py")

if __name__ == "__main__":
    main()
