# 简化版图片生成脚本（不依赖wordcloud）
# Simplified figure generation script (without wordcloud dependency)

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA, TruncatedSVD
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def generate_sample_data(n_samples=2000):
    """生成模拟Twitter数据"""
    print("生成模拟Twitter数据...")
    
    # 模拟不同主题的推文模板
    topics = {
        'work': [
            'going to work today feeling motivated', 'office meeting scheduled for afternoon', 
            'project deadline approaching need focus', 'working late tonight on presentation',
            'job interview tomorrow wish luck', 'busy day at office many tasks',
            'team collaboration project going well', 'work from home today productive',
            'conference call meeting important clients', 'career development workshop attended'
        ],
        'entertainment': [
            'watching movie tonight with friends', 'listening to music relaxing evening',
            'playing video games weekend fun', 'concert was amazing great performance',
            'new album released favorite artist', 'favorite tv show season finale',
            'weekend party plans exciting night', 'dancing all night club downtown',
            'comedy show funny laughed hard', 'theater performance absolutely stunning'
        ],
        'emotion': [
            'feeling happy today beautiful weather', 'love my family spending time',
            'sad about news world events', 'excited for vacation next week',
            'grateful for friends support always', 'missing home family gathering',
            'proud of achievement hard work', 'worried about future uncertain times',
            'feeling blessed good health happiness', 'anxious about exam results tomorrow'
        ],
        'daily_life': [
            'cooking dinner tonight special recipe', 'grocery shopping done weekly supplies',
            'cleaning house today spring cleaning', 'walking in park morning exercise',
            'morning coffee time peaceful moment', 'reading good book interesting story',
            'family dinner together quality time', 'sleeping early tonight tired day',
            'beautiful weather outside sunny day', 'weekend plans relaxing home'
        ],
        'education': [
            'studying for exam final week', 'learning new skills online course',
            'attending class today interesting lecture', 'homework assignment due tomorrow',
            'research project interesting findings', 'library study session productive',
            'online course helpful learning much', 'graduation ceremony soon excited',
            'academic conference presentation prepared', 'scholarship application submitted today'
        ]
    }
    
    # 生成样本数据
    sample_texts = []
    true_labels = []
    
    for i in range(n_samples):
        topic_keys = list(topics.keys())
        topic = topic_keys[i % len(topic_keys)]  # 确保每个主题都有数据
        base_text = np.random.choice(topics[topic])
        
        # 添加一些随机变化
        variations = [
            f"@user {base_text} #topic #life",
            f"{base_text} http://example.com/link",
            f"RT @user: {base_text} amazing",
            f"{base_text}!!! so excited",
            f"Just {base_text} wonderful day",
            base_text
        ]
        
        sample_texts.append(np.random.choice(variations))
        true_labels.append(topic)
    
    # 创建DataFrame
    data = pd.DataFrame({
        'target': np.random.choice([0, 4], n_samples),
        'id': range(n_samples),
        'date': ['Mon Apr 06 22:19:45 PDT 2009'] * n_samples,
        'flag': ['NO_QUERY'] * n_samples,
        'user': [f'user_{i%100}' for i in range(n_samples)],
        'text': sample_texts,
        'true_topic': true_labels
    })
    
    return data

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    text = re.sub(r'@\w+|#\w+', '', text)
    text = re.sub(r'[^\w\s]', '', text)
    text = text.lower()
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def generate_figure_1_dataset_sample():
    """图片1：数据集样本展示"""
    print("生成图片1：数据集样本展示...")
    
    data = generate_sample_data(10)
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 创建表格数据
    table_data = []
    for i, row in data.iterrows():
        table_data.append([
            str(row['target']),
            str(row['id'])[:8] + '...',
            row['date'][:10] + '...',
            row['user'][:10],
            row['text'][:50] + '...' if len(row['text']) > 50 else row['text']
        ])
    
    # 创建表格
    table = ax.table(cellText=table_data,
                    colLabels=['Target', 'ID', 'Date', 'User', 'Text'],
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.1, 0.15, 0.2, 0.15, 0.4])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data) + 1):
        for j in range(5):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title('Sentiment140数据集样本展示\nDataset Sample Display', 
                fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图片1_数据集样本展示.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_2_preprocessing_comparison():
    """图片2：文本预处理前后对比"""
    print("生成图片2：文本预处理前后对比...")
    
    # 示例文本
    examples = [
        "@user Check out this amazing link: http://example.com #awesome #life!!!",
        "RT @friend: Going to work today... feeling motivated! #monday #work",
        "Watching movie tonight with @family http://movielink.com so excited!!!",
        "Studying for exam tomorrow @university #education #finals stress...",
        "@user Love this song! http://music.com #music #happy dancing!!!"
    ]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 原始文本
    ax1.text(0.02, 0.95, '原始文本 (Original Text):', fontsize=14, fontweight='bold', 
             transform=ax1.transAxes, va='top')
    
    for i, text in enumerate(examples):
        ax1.text(0.02, 0.85 - i*0.15, f"{i+1}. {text}", fontsize=11, 
                transform=ax1.transAxes, va='top', wrap=True,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    # 预处理后文本
    ax2.text(0.02, 0.95, '预处理后文本 (Preprocessed Text):', fontsize=14, fontweight='bold',
             transform=ax2.transAxes, va='top')
    
    processed_examples = [preprocess_text(text) for text in examples]
    for i, text in enumerate(processed_examples):
        ax2.text(0.02, 0.85 - i*0.15, f"{i+1}. {text}", fontsize=11,
                transform=ax2.transAxes, va='top', wrap=True,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
    
    # 添加处理步骤说明
    steps_text = """
预处理步骤 (Preprocessing Steps):
• 移除URL链接 (Remove URLs)
• 移除@用户名和#标签 (Remove @mentions and #hashtags)  
• 移除标点符号 (Remove punctuation)
• 转换为小写 (Convert to lowercase)
• 移除停用词 (Remove stopwords)
• 过滤短词 (Filter short words)
    """
    
    ax2.text(0.65, 0.95, steps_text, fontsize=10, transform=ax2.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    plt.suptitle('文本预处理前后对比\nText Preprocessing Comparison', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片2_文本预处理对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_3_tfidf_distribution():
    """图片3：TF-IDF特征分布直方图"""
    print("生成图片3：TF-IDF特征分布...")
    
    data = generate_sample_data(1000)
    text_data_cleaned = data['text'].apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    
    vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.95)
    tfidf_matrix = vectorizer.fit_transform(text_data_cleaned)
    feature_names = vectorizer.get_feature_names_out()
    
    # 计算特征统计
    tfidf_dense = tfidf_matrix.toarray()
    feature_means = np.mean(tfidf_dense, axis=0)
    sparsity = np.sum(tfidf_dense == 0) / tfidf_dense.size
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. TF-IDF值分布直方图
    ax1.hist(tfidf_dense.flatten(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('TF-IDF值分布 (TF-IDF Value Distribution)', fontweight='bold')
    ax1.set_xlabel('TF-IDF值 (TF-IDF Value)')
    ax1.set_ylabel('频次 (Frequency)')
    ax1.grid(True, alpha=0.3)
    
    # 2. 特征重要性排序
    top_features_idx = np.argsort(feature_means)[-20:]
    ax2.barh(range(20), feature_means[top_features_idx], color='lightcoral')
    ax2.set_yticks(range(20))
    ax2.set_yticklabels([feature_names[i] for i in top_features_idx])
    ax2.set_title('Top 20 特征平均TF-IDF值\n(Top 20 Features by Mean TF-IDF)', fontweight='bold')
    ax2.set_xlabel('平均TF-IDF值 (Mean TF-IDF Value)')
    
    # 3. 稀疏性可视化
    sample_matrix = tfidf_dense[:50, :50]  # 取样本进行可视化
    im = ax3.imshow(sample_matrix, cmap='Blues', aspect='auto')
    ax3.set_title('TF-IDF矩阵稀疏性可视化\n(TF-IDF Matrix Sparsity)', fontweight='bold')
    ax3.set_xlabel('特征索引 (Feature Index)')
    ax3.set_ylabel('文档索引 (Document Index)')
    plt.colorbar(im, ax=ax3)
    
    # 4. 统计信息
    stats_text = f"""
TF-IDF矩阵统计信息
(TF-IDF Matrix Statistics)

• 文档数量: {tfidf_dense.shape[0]:,}
• 特征数量: {tfidf_dense.shape[1]:,}
• 矩阵大小: {tfidf_dense.shape[0]:,} × {tfidf_dense.shape[1]:,}
• 稀疏度: {sparsity:.1%}
• 非零元素: {np.sum(tfidf_dense > 0):,}
• 平均TF-IDF值: {np.mean(tfidf_dense):.4f}
• 最大TF-IDF值: {np.max(tfidf_dense):.4f}
• 标准差: {np.std(tfidf_dense):.4f}
    """
    
    ax4.text(0.1, 0.9, stats_text, fontsize=12, transform=ax4.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('图片3_TFIDF特征分布.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return tfidf_dense, feature_names, text_data_cleaned

def main():
    """主函数 - 生成前3个图片"""
    print("开始生成论文图片（简化版）...")
    print("=" * 60)
    
    # 生成图片1：数据集样本展示
    generate_figure_1_dataset_sample()
    
    # 生成图片2：文本预处理对比
    generate_figure_2_preprocessing_comparison()
    
    # 生成图片3：TF-IDF特征分布
    tfidf_matrix, feature_names, text_data_cleaned = generate_figure_3_tfidf_distribution()
    
    print("\n" + "=" * 60)
    print("✅ 前3个图片生成完成！")
    print("\n生成的图片文件：")
    print("1. 图片1_数据集样本展示.png")
    print("2. 图片2_文本预处理对比.png") 
    print("3. 图片3_TFIDF特征分布.png")
    print("\n请将这些图片插入到论文的相应位置！")

if __name__ == "__main__":
    main()
