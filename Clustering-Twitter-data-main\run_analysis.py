# 运行Twitter文本聚类分析的主脚本
# Main script to run Twitter text clustering analysis

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def check_dependencies():
    """检查必要的依赖库"""
    required_packages = [
        'pandas', 'numpy', 'scikit-learn', 'matplotlib', 
        'seaborn', 'wordcloud'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包，请先安装：")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    print("所有依赖包检查通过！")
    return True

def check_data_file():
    """检查数据文件是否存在"""
    if not os.path.exists("twitter.csv"):
        print("错误：未找到twitter.csv数据文件")
        print("请确保Sentiment140数据集文件位于当前目录下")
        print("数据集下载地址：http://help.sentiment140.com/for-students/")
        return False
    
    print("数据文件检查通过！")
    return True

def run_clustering_analysis():
    """运行聚类分析"""
    print("开始运行聚类分析...")
    print("=" * 50)
    
    try:
        # 运行主要的聚类分析脚本
        exec(open('Clustering_twitter.py').read())
        print("聚类分析完成！")
        return True
    except Exception as e:
        print(f"聚类分析运行出错：{e}")
        return False

def run_wordcloud_analysis():
    """运行词云分析"""
    print("\n开始运行词云分析...")
    print("=" * 50)
    
    try:
        # 运行词云分析脚本
        exec(open('wordcloud_analysis.py').read())
        print("词云分析完成！")
        return True
    except Exception as e:
        print(f"词云分析运行出错：{e}")
        return False

def generate_report():
    """生成分析报告"""
    print("\n生成分析报告...")
    
    # 检查生成的文件
    expected_files = [
        'clustering_results_comparison.png',
        'optimal_clusters_analysis.png',
        'overall_wordcloud.png'
    ]
    
    generated_files = []
    missing_files = []
    
    for file in expected_files:
        if os.path.exists(file):
            generated_files.append(file)
        else:
            missing_files.append(file)
    
    print(f"成功生成 {len(generated_files)} 个文件：")
    for file in generated_files:
        print(f"  ✓ {file}")
    
    if missing_files:
        print(f"未生成 {len(missing_files)} 个文件：")
        for file in missing_files:
            print(f"  ✗ {file}")
    
    # 检查词云文件
    wordcloud_files = [f for f in os.listdir('.') if f.startswith('wordcloud_cluster_')]
    if wordcloud_files:
        print(f"生成了 {len(wordcloud_files)} 个聚类词云文件")
    
    return len(generated_files) > 0

def main():
    """主函数"""
    print("Twitter文本聚类分析系统")
    print("=" * 50)
    print(f"开始时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境
    if not check_dependencies():
        return
    
    if not check_data_file():
        return
    
    # 运行分析
    success = True
    
    # 1. 运行聚类分析
    if not run_clustering_analysis():
        success = False
    
    # 2. 运行词云分析
    if not run_wordcloud_analysis():
        success = False
    
    # 3. 生成报告
    if not generate_report():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 分析完成！所有任务执行成功")
        print("\n生成的文件可用于论文插图：")
        print("1. clustering_results_comparison.png - 聚类结果对比图")
        print("2. optimal_clusters_analysis.png - 最优聚类数分析图")
        print("3. overall_wordcloud.png - 整体词云图")
        print("4. wordcloud_cluster_*.png - 各聚类词云图")
        print("\n论文文档：基于Twitter数据的社交媒体文本聚类分析论文.md")
    else:
        print("✗ 分析过程中出现错误，请检查日志信息")
    
    print(f"结束时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
