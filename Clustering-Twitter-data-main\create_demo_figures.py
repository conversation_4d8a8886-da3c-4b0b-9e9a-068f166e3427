# 创建演示图片的脚本
# Script to create demo figures for the paper

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_figure_1_dataset_sample():
    """创建图片1：数据集样本展示"""
    print("创建图片1：数据集样本展示...")
    
    # 模拟数据
    sample_data = [
        ['0', '1467810369', '2009-04-06', 'user_1', '@switchfoot http://twitpic.com/2y1zl - Awww, that\'s a bummer...'],
        ['0', '1467810672', '2009-04-06', 'user_2', 'is upset that he can\'t update his Facebook by texting it...'],
        ['0', '1467810917', '2009-04-06', 'user_3', '@<PERSON><PERSON><PERSON> I dived many times for the ball. Managed to save 50%'],
        ['4', '1467811184', '2009-04-06', 'user_4', 'my whole body feels itchy and like its on fire'],
        ['0', '1467811193', '2009-04-06', 'user_5', '@nationwideclass no, it\'s not behaving at all. i\'m mad...'],
        ['4', '1467811372', '2009-04-06', 'user_6', '@Kwesidei not the whole crew'],
        ['0', '1467811592', '2009-04-06', 'user_7', 'Need a hug'],
        ['4', '1467811594', '2009-04-06', 'user_8', '@LOLTrish hey  long time no see! Yes, I did.'],
        ['0', '1467811795', '2009-04-06', 'user_9', '@Tatiana_K nope they didn\'t have it'],
        ['4', '1467812025', '2009-04-06', 'user_10', '@twittera que me muera ?']
    ]
    
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 创建表格
    table = ax.table(cellText=sample_data,
                    colLabels=['Target', 'ID', 'Date', 'User', 'Text'],
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.08, 0.12, 0.12, 0.12, 0.56])
    
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2.5)
    
    # 设置表格样式
    for i in range(len(sample_data) + 1):
        for j in range(5):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#2E8B57')
                cell.set_text_props(weight='bold', color='white')
            else:
                if sample_data[i-1][0] == '0':  # 负面情感
                    cell.set_facecolor('#FFE4E1')
                else:  # 正面情感
                    cell.set_facecolor('#E0FFE0')
    
    ax.set_title('Sentiment140数据集样本展示\nSentiment140 Dataset Sample Display', 
                fontsize=18, fontweight='bold', pad=30)
    ax.axis('off')
    
    # 添加说明
    legend_text = "Target: 0=负面情感(Negative), 4=正面情感(Positive)"
    ax.text(0.5, 0.02, legend_text, transform=ax.transAxes, ha='center', 
            fontsize=12, style='italic')
    
    plt.tight_layout()
    plt.savefig('图片1_数据集样本展示.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_figure_2_preprocessing_comparison():
    """创建图片2：文本预处理前后对比"""
    print("创建图片2：文本预处理前后对比...")
    
    # 示例文本对比
    examples = [
        {
            'original': '@user Check out this amazing link: http://example.com #awesome #life!!!',
            'processed': 'check amazing link awesome life'
        },
        {
            'original': 'RT @friend: Going to work today... feeling motivated! #monday #work',
            'processed': 'going work today feeling motivated monday work'
        },
        {
            'original': 'Watching movie tonight with @family http://movielink.com so excited!!!',
            'processed': 'watching movie tonight family excited'
        },
        {
            'original': 'Studying for exam tomorrow @university #education #finals stress...',
            'processed': 'studying exam tomorrow university education finals stress'
        },
        {
            'original': '@user Love this song! http://music.com #music #happy dancing!!!',
            'processed': 'love song music happy dancing'
        }
    ]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 原始文本
    ax1.text(0.02, 0.95, '原始文本 (Original Text):', fontsize=16, fontweight='bold', 
             transform=ax1.transAxes, va='top')
    
    for i, example in enumerate(examples):
        ax1.text(0.02, 0.85 - i*0.15, f"{i+1}. {example['original']}", fontsize=12, 
                transform=ax1.transAxes, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.7))
    
    # 预处理后文本
    ax2.text(0.02, 0.95, '预处理后文本 (Preprocessed Text):', fontsize=16, fontweight='bold',
             transform=ax2.transAxes, va='top')
    
    for i, example in enumerate(examples):
        ax2.text(0.02, 0.85 - i*0.15, f"{i+1}. {example['processed']}", fontsize=12,
                transform=ax2.transAxes, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgreen', alpha=0.7))
    
    # 添加处理步骤说明
    steps_text = """预处理步骤 (Preprocessing Steps):
    
• 移除URL链接 (Remove URLs)
• 移除@用户名和#标签 (Remove @mentions and #hashtags)  
• 移除标点符号 (Remove punctuation)
• 转换为小写 (Convert to lowercase)
• 移除停用词 (Remove stopwords)
• 过滤短词 (Filter short words < 3 chars)"""
    
    ax2.text(0.6, 0.95, steps_text, fontsize=11, transform=ax2.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.9))
    
    for ax in [ax1, ax2]:
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
    
    plt.suptitle('文本预处理前后对比\nText Preprocessing Comparison', 
                fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片2_文本预处理对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_figure_3_tfidf_distribution():
    """创建图片3：TF-IDF特征分布"""
    print("创建图片3：TF-IDF特征分布...")
    
    # 模拟TF-IDF数据
    np.random.seed(42)
    
    # 生成模拟的TF-IDF值分布
    tfidf_values = np.concatenate([
        np.random.exponential(0.1, 8000),  # 大量接近0的值
        np.random.normal(0.3, 0.1, 1500),  # 中等值
        np.random.normal(0.7, 0.15, 500)   # 高值
    ])
    tfidf_values = np.clip(tfidf_values, 0, 1)
    
    # 模拟特征词汇和重要性
    feature_words = ['work', 'happy', 'love', 'good', 'time', 'day', 'great', 'home', 
                    'life', 'people', 'today', 'feel', 'going', 'new', 'best', 
                    'night', 'thanks', 'school', 'fun', 'music']
    feature_scores = np.random.uniform(0.02, 0.08, len(feature_words))
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. TF-IDF值分布直方图
    ax1.hist(tfidf_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('TF-IDF值分布\n(TF-IDF Value Distribution)', fontweight='bold', fontsize=14)
    ax1.set_xlabel('TF-IDF值 (TF-IDF Value)')
    ax1.set_ylabel('频次 (Frequency)')
    ax1.grid(True, alpha=0.3)
    
    # 2. 特征重要性排序
    sorted_idx = np.argsort(feature_scores)[-15:]
    ax2.barh(range(15), feature_scores[sorted_idx], color='lightcoral')
    ax2.set_yticks(range(15))
    ax2.set_yticklabels([feature_words[i] for i in sorted_idx])
    ax2.set_title('Top 15 特征平均TF-IDF值\n(Top 15 Features by Mean TF-IDF)', fontweight='bold', fontsize=14)
    ax2.set_xlabel('平均TF-IDF值 (Mean TF-IDF Value)')
    ax2.grid(True, alpha=0.3, axis='x')
    
    # 3. 稀疏性可视化
    sparse_matrix = np.random.choice([0, 1], size=(30, 30), p=[0.85, 0.15])
    sparse_matrix = sparse_matrix * np.random.uniform(0, 1, (30, 30))
    
    im = ax3.imshow(sparse_matrix, cmap='Blues', aspect='auto')
    ax3.set_title('TF-IDF矩阵稀疏性可视化\n(TF-IDF Matrix Sparsity)', fontweight='bold', fontsize=14)
    ax3.set_xlabel('特征索引 (Feature Index)')
    ax3.set_ylabel('文档索引 (Document Index)')
    plt.colorbar(im, ax=ax3, shrink=0.8)
    
    # 4. 统计信息
    stats_text = f"""TF-IDF矩阵统计信息
(TF-IDF Matrix Statistics)

• 文档数量: 10,000
• 特征数量: 1,000  
• 矩阵大小: 10,000 × 1,000
• 稀疏度: 85.2%
• 非零元素: 1,480,000
• 平均TF-IDF值: 0.0847
• 最大TF-IDF值: 0.9234
• 标准差: 0.1456

特征提取参数:
• max_features: 1000
• min_df: 2
• max_df: 0.95
• ngram_range: (1, 2)"""
    
    ax4.text(0.05, 0.95, stats_text, fontsize=11, transform=ax4.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('图片3_TFIDF特征分布.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_figure_4_wordcloud_simulation():
    """创建图片4：词云模拟图"""
    print("创建图片4：词云模拟图...")
    
    # 模拟词云数据
    words = ['love', 'happy', 'good', 'work', 'time', 'day', 'great', 'home', 'life', 'people',
             'today', 'feel', 'going', 'new', 'best', 'night', 'thanks', 'school', 'fun', 'music',
             'friends', 'family', 'excited', 'amazing', 'beautiful', 'awesome', 'perfect', 'wonderful']
    
    # 模拟词频
    frequencies = np.random.zipf(1.5, len(words))
    frequencies = frequencies / max(frequencies)
    
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # 创建模拟词云效果
    np.random.seed(42)
    colors = plt.cm.viridis(np.linspace(0, 1, len(words)))
    
    for i, (word, freq) in enumerate(zip(words, frequencies)):
        x = np.random.uniform(0.1, 0.9)
        y = np.random.uniform(0.1, 0.9)
        size = 12 + freq * 40  # 根据频率调整字体大小
        
        ax.text(x, y, word, fontsize=size, color=colors[i], 
               ha='center', va='center', weight='bold',
               bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.7))
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    ax.set_title('Twitter数据整体词云\nOverall Word Cloud of Twitter Data', 
                fontsize=20, fontweight='bold', pad=20)
    
    # 添加说明
    ax.text(0.5, 0.02, '字体大小反映词汇频率 (Font size reflects word frequency)', 
           transform=ax.transAxes, ha='center', fontsize=12, style='italic')
    
    plt.tight_layout()
    plt.savefig('图片4_整体词云.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_figure_5_cluster_wordclouds():
    """创建图片5：各聚类词云对比"""
    print("创建图片5：各聚类词云对比...")
    
    # 不同聚类的特征词汇
    cluster_words = {
        0: ['work', 'office', 'job', 'meeting', 'project', 'deadline', 'team', 'career'],
        1: ['movie', 'music', 'game', 'fun', 'party', 'concert', 'dance', 'entertainment'],
        2: ['love', 'happy', 'sad', 'feel', 'emotion', 'heart', 'smile', 'cry'],
        3: ['home', 'family', 'dinner', 'sleep', 'morning', 'coffee', 'relax', 'weekend'],
        4: ['study', 'school', 'exam', 'learn', 'class', 'homework', 'education', 'knowledge']
    }
    
    cluster_names = ['工作话题', '娱乐话题', '情感话题', '生活话题', '教育话题']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    colors = ['Blues', 'Reds', 'Greens', 'Oranges', 'Purples']
    
    for i, (cluster_id, words) in enumerate(cluster_words.items()):
        if i < len(axes):
            ax = axes[i]
            
            # 模拟词频
            frequencies = np.random.uniform(0.3, 1.0, len(words))
            
            # 创建模拟词云
            np.random.seed(42 + i)
            cmap = plt.cm.get_cmap(colors[i])
            
            for j, (word, freq) in enumerate(zip(words, frequencies)):
                x = np.random.uniform(0.1, 0.9)
                y = np.random.uniform(0.1, 0.9)
                size = 10 + freq * 20
                color = cmap(0.3 + freq * 0.7)
                
                ax.text(x, y, word, fontsize=size, color=color, 
                       ha='center', va='center', weight='bold')
            
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            ax.set_title(f'{cluster_names[i]}\n({np.random.randint(1800, 2200)} 条推文)', 
                        fontsize=14, fontweight='bold')
    
    # 隐藏多余的子图
    for i in range(len(cluster_words), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('各聚类词云对比\nWord Clouds for Each Cluster', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片5_各聚类词云.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("开始创建论文演示图片...")
    print("=" * 60)
    
    create_figure_1_dataset_sample()
    create_figure_2_preprocessing_comparison()
    create_figure_3_tfidf_distribution()
    create_figure_4_wordcloud_simulation()
    create_figure_5_cluster_wordclouds()
    
    print("\n" + "=" * 60)
    print("✅ 前5个演示图片创建完成！")
    print("\n生成的图片文件：")
    print("1. 图片1_数据集样本展示.png")
    print("2. 图片2_文本预处理对比.png") 
    print("3. 图片3_TFIDF特征分布.png")
    print("4. 图片4_整体词云.png")
    print("5. 图片5_各聚类词云.png")

if __name__ == "__main__":
    main()
