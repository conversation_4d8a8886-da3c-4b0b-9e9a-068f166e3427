# Twitter文本聚类词云分析
# Word Cloud Analysis for Twitter Text Clustering

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from wordcloud import WordCloud
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def generate_wordcloud_for_cluster(texts, cluster_id, save_path=None):
    """为特定聚类生成词云"""
    # 合并聚类中的所有文本
    cluster_text = ' '.join(texts)
    
    if len(cluster_text.strip()) == 0:
        print(f"聚类 {cluster_id} 没有有效文本")
        return None
    
    # 生成词云
    wordcloud = WordCloud(
        width=800, 
        height=400, 
        background_color='white',
        max_words=100,
        colormap='viridis',
        relative_scaling=0.5,
        random_state=42
    ).generate(cluster_text)
    
    # 显示词云
    plt.figure(figsize=(10, 5))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(f'聚类 {cluster_id} 词云', fontsize=16, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()
    return wordcloud

def main():
    """主函数"""
    print("开始词云分析...")
    
    # 加载数据
    try:
        columns = ['target', 'id', 'date', 'flag', 'user', 'text']
        data = pd.read_csv("twitter.csv", encoding='latin-1', header=None, names=columns)
        print(f"数据加载成功！数据形状: {data.shape}")
    except FileNotFoundError:
        print("错误：未找到twitter.csv文件")
        return
    
    # 文本预处理
    print("进行文本预处理...")
    text_data = data['text']
    text_data_cleaned = text_data.apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    
    # 采样数据
    sampled_data = text_data_cleaned.sample(n=min(5000, len(text_data_cleaned)), random_state=42)
    print(f"采样数据量: {len(sampled_data)}")
    
    # TF-IDF向量化
    vectorizer = TfidfVectorizer(max_features=1000, min_df=2, max_df=0.95)
    text_vectors = vectorizer.fit_transform(sampled_data)
    
    # K-Means聚类
    optimal_k = 5  # 可以根据需要调整
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    labels = kmeans.fit_predict(text_vectors)
    
    print(f"聚类完成，共{optimal_k}个聚类")
    
    # 为每个聚类生成词云
    print("生成各聚类词云...")
    for cluster_id in range(optimal_k):
        cluster_texts = sampled_data[labels == cluster_id]
        if len(cluster_texts) > 0:
            print(f"生成聚类 {cluster_id} 词云 (包含 {len(cluster_texts)} 条推文)")
            generate_wordcloud_for_cluster(
                cluster_texts.values, 
                cluster_id, 
                f'wordcloud_cluster_{cluster_id}.png'
            )
    
    # 生成整体词云
    print("生成整体词云...")
    all_text = ' '.join(sampled_data.values)
    overall_wordcloud = WordCloud(
        width=1200, 
        height=600, 
        background_color='white',
        max_words=200,
        colormap='plasma',
        relative_scaling=0.5,
        random_state=42
    ).generate(all_text)
    
    plt.figure(figsize=(15, 8))
    plt.imshow(overall_wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title('Twitter数据整体词云', fontsize=20, fontweight='bold')
    plt.savefig('overall_wordcloud.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("词云分析完成！")
    print("生成的文件:")
    for i in range(optimal_k):
        print(f"  - wordcloud_cluster_{i}.png")
    print("  - overall_wordcloud.png")

if __name__ == "__main__":
    main()
