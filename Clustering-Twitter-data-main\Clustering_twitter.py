# 基于Sentiment140数据集的Twitter推文聚类分析
# Twitter Text Clustering Analysis Based on Sentiment140 Dataset

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import silhouette_score, adjusted_rand_score, calinski_harabasz_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 第一步：加载数据集
# Step 1: Load the dataset
print("正在加载数据集...")
# 由于CSV文件没有标题行，需要手动指定列名
columns = ['target', 'id', 'date', 'flag', 'user', 'text']
try:
    data = pd.read_csv("twitter.csv", encoding='latin-1', header=None, names=columns)
    print(f"数据集加载成功！数据形状: {data.shape}")
    print("数据集前5行预览:")
    print(data.head())
except FileNotFoundError:
    print("错误：未找到twitter.csv文件，请确保文件在当前目录下")
    exit()

# 提取文本数据用于聚类分析
text_data = data['text']
print(f"文本数据总数: {len(text_data)}")

# 第二步：文本数据预处理
# Step 2: Text Data Preprocessing
print("\n开始文本预处理...")
# 移除停用词、标点符号并转换为小写，这有助于减少噪声、标准化文本，
# 提高特征质量以获得更好的模型性能

# 定义文本预处理函数
def preprocess_text(text):
    """
    文本预处理函数
    - 移除URL链接
    - 移除标点符号
    - 转换为小写
    - 移除停用词
    """
    if pd.isna(text):
        return ""

    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

print("正在进行文本清洗...")
text_data_cleaned = text_data.apply(preprocess_text)
# 移除空文本
text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
print(f"清洗后的文本数据: {len(text_data_cleaned)}")

# 为了计算效率，随机采样10000条数据
print("正在采样数据...")
sampled_data = text_data_cleaned.sample(n=min(10000, len(text_data_cleaned)), random_state=42)
print(f"采样数据量: {len(sampled_data)}")

# 第三步：TF-IDF向量化
# Step 3: TF-IDF Vectorization
print("\n开始TF-IDF向量化...")
"""
TF-IDF (Term Frequency-Inverse Document Frequency) 的优势：
1. 提供词汇的加权表示，捕获词汇在文档中的重要性
2. 有助于识别文档中的重要词汇，降低常见无意义词汇的权重
3. 适用于文本聚类、分类和信息检索等任务
4. 与简单词频统计相比，TF-IDF为特定文档中的特殊词汇分配更高的重要性

TF (词频): 衡量词汇在文档中出现的频率
IDF (逆文档频率): 衡量词汇在整个语料库中的重要性，在多个文档中出现的词汇重要性较低
"""

# 限制特征数量以简化计算（选择最重要的1000个词汇特征）
vectorizer = TfidfVectorizer(
    max_features=1000,          # 最大特征数
    min_df=2,                   # 词汇至少在2个文档中出现
    max_df=0.95,                # 词汇最多在95%的文档中出现
    ngram_range=(1, 2)          # 使用1-gram和2-gram
)

# 将清洗后的文本数据转换为TF-IDF特征向量
text_vectors = vectorizer.fit_transform(sampled_data).toarray()
print(f"TF-IDF向量化完成！特征维度: {text_vectors.shape}")

# 获取特征词汇
feature_names = vectorizer.get_feature_names_out()
print(f"特征词汇数量: {len(feature_names)}")
print("前10个特征词汇:", feature_names[:10])

# 第四步：K-Means聚类
# Step 4: K-Means Clustering
print("\n开始K-Means聚类...")
# 使用肘部法则确定最优聚类数
def find_optimal_clusters(data, max_k=10):
    """使用肘部法则和轮廓系数确定最优聚类数"""
    inertias = []
    silhouette_scores = []
    K_range = range(2, max_k + 1)

    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(data)
        inertias.append(kmeans.inertia_)
        silhouette_scores.append(silhouette_score(data, kmeans.labels_))

    return K_range, inertias, silhouette_scores

# 寻找最优聚类数
K_range, inertias, silhouette_scores = find_optimal_clusters(text_vectors, max_k=8)
optimal_k = K_range[np.argmax(silhouette_scores)]
print(f"基于轮廓系数的最优聚类数: {optimal_k}")

# 执行K-Means聚类
kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
kmeans_labels = kmeans.fit_predict(text_vectors)

# 评估K-Means聚类效果
kmeans_silhouette = silhouette_score(text_vectors, kmeans_labels)
kmeans_calinski = calinski_harabasz_score(text_vectors, kmeans_labels)
print(f"K-Means聚类结果:")
print(f"  轮廓系数 (Silhouette Score): {kmeans_silhouette:.3f}")
print(f"  Calinski-Harabasz指数: {kmeans_calinski:.3f}")

# 显示各聚类的大小
unique, counts = np.unique(kmeans_labels, return_counts=True)
print(f"  各聚类大小: {dict(zip(unique, counts))}")

# Step 5: DBSCAN Clustering (# Adjust parameters)
dbscan= DBSCAN(eps=0.5, min_samples=10, metric='euclidean') #The metric='euclidean' specifies that the Euclidean distance should be used t
dbscan_labels = dbscan.fit_predict(text_vectors)

# Filter noise (-1 indicates noise points)
'''The step dbscan_core_samples = sum(dbscan_labels != -1) counts the number of core samples in DBSCAN, where -1 indicates noise points.
This step is important for evaluating how many points belong to clusters, as DBSCAN can mark some points as noise,
and the number of core samples helps understand the density of the formed clusters. Unlike K-means, DBSCAN does not require
the user to specify the number of clusters, and this step is useful to assess the clustering quality.'''

dbscan_core_samples = sum(dbscan_labels != -1)
print(f"DBSCAN Core Samples: {dbscan_core_samples}")
'''the silhouette score wasn't calculated for DBSCAN because it is typically used for algorithms like K-means, where each point
belongs to a single cluster. In DBSCAN, some points are marked as noise (-1), and the silhouette score may not be meaningful for those points'''

# Step 6: Agglomerative Clustering
#linkage='ward' refers to the method used in hierarchical clustering to minimize the variance within clusters as
# they are merged, aiming to create compact and homogeneous clusters.
agg = AgglomerativeClustering(n_clusters=5, metric='euclidean', linkage='ward')
agg_labels = agg.fit_predict(text_vectors)

#Evaluate Agglomerative Clustering using Silhouette Score
agg_silhouette = silhouette_score(text_vectors, agg_labels)
print(f"Agglomerative Silhouette Score: {agg_silhouette:.2f}")

# Step 7: Visualize Clustering Results (Using PCA for Dimensionality Reduction)
from sklearn.decomposition import PCA

pca = PCA(n_components=2)
text_vectors_2d = pca.fit_transform(text_vectors)

plt.figure(figsize=(15, 5))
#The numbers in plt.subplot(1, 3, 1) indicate the grid layout for the subplots: 1 row, 3 columns, and this is the 1st subplot in that grid.
# KMeans
plt.subplot(1, 3, 1)
plt.scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], c=kmeans_labels, cmap='viridis', s=10)
plt.title("KMeans Clustering")

# DBSCAN
plt.subplot(1, 3, 2)
plt.scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], c=dbscan_labels, cmap='viridis', s=10)
plt.title("DBSCAN Clustering")

# Agglomerative Clustering
plt.subplot(1, 3, 3)
plt.scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], c=agg_labels, cmap='viridis', s=10)
plt.title("Agglomerative Clustering")

plt.show()

# Step 8: Conclusion
print("Clustering Complete!")