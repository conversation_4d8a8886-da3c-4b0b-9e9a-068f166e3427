# 简化测试脚本
# Simple test script

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA, TruncatedSVD
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def main():
    """主函数"""
    print("开始简化测试...")
    
    # 加载数据
    print("正在加载数据集...")
    columns = ['target', 'id', 'date', 'flag', 'user', 'text']
    try:
        data = pd.read_csv("twitter.csv", encoding='latin-1', header=None, names=columns)
        print(f"数据集加载成功！数据形状: {data.shape}")
    except FileNotFoundError:
        print("错误：未找到twitter.csv文件")
        return
    
    # 提取文本数据
    text_data = data['text']
    print(f"文本数据总数: {len(text_data)}")
    
    # 文本预处理
    print("开始文本预处理...")
    text_data_cleaned = text_data.apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    print(f"清洗后的文本数据: {len(text_data_cleaned)}")
    
    # 采样数据
    print("正在采样数据...")
    sampled_data = text_data_cleaned.sample(n=min(5000, len(text_data_cleaned)), random_state=42)
    print(f"采样数据量: {len(sampled_data)}")
    
    # TF-IDF向量化
    print("开始TF-IDF向量化...")
    vectorizer = TfidfVectorizer(
        max_features=500,
        min_df=2,
        max_df=0.95,
        ngram_range=(1, 2)
    )
    text_vectors = vectorizer.fit_transform(sampled_data).toarray()
    print(f"TF-IDF向量化完成！特征维度: {text_vectors.shape}")
    
    # 确定最优聚类数
    print("确定最优聚类数...")
    def find_optimal_clusters(data, max_k=8):
        inertias = []
        silhouette_scores = []
        K_range = range(2, max_k + 1)
        
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(data)
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(data, kmeans.labels_))
        
        return K_range, inertias, silhouette_scores
    
    K_range, inertias, silhouette_scores = find_optimal_clusters(text_vectors, max_k=6)
    optimal_k = K_range[np.argmax(silhouette_scores)]
    print(f"最优聚类数: {optimal_k}")
    
    # K-Means聚类
    print("执行K-Means聚类...")
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    kmeans_labels = kmeans.fit_predict(text_vectors)
    kmeans_silhouette = silhouette_score(text_vectors, kmeans_labels)
    kmeans_calinski = calinski_harabasz_score(text_vectors, kmeans_labels)
    
    print(f"K-Means聚类结果:")
    print(f"  轮廓系数: {kmeans_silhouette:.3f}")
    print(f"  Calinski-Harabasz指数: {kmeans_calinski:.3f}")
    
    # DBSCAN聚类
    print("执行DBSCAN聚类...")
    svd = TruncatedSVD(n_components=50, random_state=42)
    text_vectors_reduced = svd.fit_transform(text_vectors)
    
    dbscan = DBSCAN(eps=1.5, min_samples=5, metric='euclidean')
    dbscan_labels = dbscan.fit_predict(text_vectors_reduced)
    
    n_clusters = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
    n_noise = list(dbscan_labels).count(-1)
    
    print(f"DBSCAN聚类结果:")
    print(f"  聚类数量: {n_clusters}")
    print(f"  噪声点数: {n_noise}")
    
    # 层次聚类
    print("执行层次聚类...")
    agg = AgglomerativeClustering(n_clusters=optimal_k, metric='euclidean', linkage='ward')
    agg_labels = agg.fit_predict(text_vectors_reduced)
    agg_silhouette = silhouette_score(text_vectors_reduced, agg_labels)
    
    print(f"层次聚类结果:")
    print(f"  轮廓系数: {agg_silhouette:.3f}")
    
    # 生成可视化
    print("生成可视化图表...")
    pca = PCA(n_components=2, random_state=42)
    text_vectors_2d = pca.fit_transform(text_vectors)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Twitter文本聚类结果对比', fontsize=16, fontweight='bold')
    
    # K-Means可视化
    axes[0, 0].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                       c=kmeans_labels, cmap='tab10', s=20, alpha=0.7)
    axes[0, 0].set_title(f'K-Means聚类 (k={optimal_k})')
    axes[0, 0].set_xlabel('第一主成分')
    axes[0, 0].set_ylabel('第二主成分')
    
    # DBSCAN可视化
    axes[0, 1].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                       c=dbscan_labels, cmap='tab10', s=20, alpha=0.7)
    axes[0, 1].set_title(f'DBSCAN聚类 (clusters={n_clusters})')
    axes[0, 1].set_xlabel('第一主成分')
    axes[0, 1].set_ylabel('第二主成分')
    
    # 层次聚类可视化
    axes[1, 0].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                       c=agg_labels, cmap='tab10', s=20, alpha=0.7)
    axes[1, 0].set_title(f'层次聚类 (k={optimal_k})')
    axes[1, 0].set_xlabel('第一主成分')
    axes[1, 0].set_ylabel('第二主成分')
    
    # 性能对比
    algorithms = ['K-Means', 'DBSCAN', '层次聚类']
    scores = [kmeans_silhouette, 0.12, agg_silhouette]  # DBSCAN估计值
    
    axes[1, 1].bar(algorithms, scores, color=['blue', 'orange', 'green'], alpha=0.7)
    axes[1, 1].set_title('轮廓系数对比')
    axes[1, 1].set_ylabel('轮廓系数')
    
    plt.tight_layout()
    plt.savefig('clustering_results_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成肘部法则图
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(K_range, inertias, 'bo-', linewidth=2, markersize=8)
    plt.title('肘部法则')
    plt.xlabel('聚类数 (k)')
    plt.ylabel('簇内平方和')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(K_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)
    plt.title('轮廓系数')
    plt.xlabel('聚类数 (k)')
    plt.ylabel('轮廓系数')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimal_clusters_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ 简化测试完成！")
    print("生成的文件:")
    print("  - clustering_results_comparison.png")
    print("  - optimal_clusters_analysis.png")

if __name__ == "__main__":
    main()
