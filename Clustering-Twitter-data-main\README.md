# Clustering Twitter Data: Sentiment140 Dataset

## Project Overview
This project demonstrates clustering techniques on textual data using the Sentiment140 dataset. By applying algorithms like K-Means and DBSCAN, we group tweets based on their content to uncover patterns and trends.

## Dataset Used
- **Dataset**: Sentiment140
- **Purpose**: Focused on clustering tweet text.

## Technologies Used
1. Python Libraries:
   - <PERSON><PERSON><PERSON>, <PERSON>das
   - <PERSON>, <PERSON><PERSON>
   - <PERSON>n (clustering models, metrics, preprocessing)
2. NLP Tools:
   - TF-IDF Vectorizer
   - Tokenization and stopword removal

## Future Work
- Use Word2Vec for richer embeddings.
- Explore hierarchical clustering for additional insights.
