# 生成论文所需图片的脚本
# Script to generate all figures needed for the paper

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA, TruncatedSVD
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def generate_sample_data(n_samples=2000):
    """生成模拟Twitter数据"""
    print("生成模拟Twitter数据...")
    
    # 模拟不同主题的推文模板
    topics = {
        'work': [
            'going to work today feeling motivated', 'office meeting scheduled for afternoon', 
            'project deadline approaching need focus', 'working late tonight on presentation',
            'job interview tomorrow wish luck', 'busy day at office many tasks',
            'team collaboration project going well', 'work from home today productive',
            'conference call meeting important clients', 'career development workshop attended'
        ],
        'entertainment': [
            'watching movie tonight with friends', 'listening to music relaxing evening',
            'playing video games weekend fun', 'concert was amazing great performance',
            'new album released favorite artist', 'favorite tv show season finale',
            'weekend party plans exciting night', 'dancing all night club downtown',
            'comedy show funny laughed hard', 'theater performance absolutely stunning'
        ],
        'emotion': [
            'feeling happy today beautiful weather', 'love my family spending time',
            'sad about news world events', 'excited for vacation next week',
            'grateful for friends support always', 'missing home family gathering',
            'proud of achievement hard work', 'worried about future uncertain times',
            'feeling blessed good health happiness', 'anxious about exam results tomorrow'
        ],
        'daily_life': [
            'cooking dinner tonight special recipe', 'grocery shopping done weekly supplies',
            'cleaning house today spring cleaning', 'walking in park morning exercise',
            'morning coffee time peaceful moment', 'reading good book interesting story',
            'family dinner together quality time', 'sleeping early tonight tired day',
            'beautiful weather outside sunny day', 'weekend plans relaxing home'
        ],
        'education': [
            'studying for exam final week', 'learning new skills online course',
            'attending class today interesting lecture', 'homework assignment due tomorrow',
            'research project interesting findings', 'library study session productive',
            'online course helpful learning much', 'graduation ceremony soon excited',
            'academic conference presentation prepared', 'scholarship application submitted today'
        ]
    }
    
    # 生成样本数据
    sample_texts = []
    true_labels = []
    
    for i in range(n_samples):
        topic_keys = list(topics.keys())
        topic = topic_keys[i % len(topic_keys)]  # 确保每个主题都有数据
        base_text = np.random.choice(topics[topic])
        
        # 添加一些随机变化
        variations = [
            f"@user {base_text} #topic #life",
            f"{base_text} http://example.com/link",
            f"RT @user: {base_text} amazing",
            f"{base_text}!!! so excited",
            f"Just {base_text} wonderful day",
            base_text
        ]
        
        sample_texts.append(np.random.choice(variations))
        true_labels.append(topic)
    
    # 创建DataFrame
    data = pd.DataFrame({
        'target': np.random.choice([0, 4], n_samples),
        'id': range(n_samples),
        'date': ['Mon Apr 06 22:19:45 PDT 2009'] * n_samples,
        'flag': ['NO_QUERY'] * n_samples,
        'user': [f'user_{i%100}' for i in range(n_samples)],
        'text': sample_texts,
        'true_topic': true_labels
    })
    
    return data

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    text = re.sub(r'@\w+|#\w+', '', text)
    text = re.sub(r'[^\w\s]', '', text)
    text = text.lower()
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def generate_figure_1_dataset_sample():
    """图片1：数据集样本展示"""
    print("生成图片1：数据集样本展示...")
    
    data = generate_sample_data(10)
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 创建表格数据
    table_data = []
    for i, row in data.iterrows():
        table_data.append([
            str(row['target']),
            str(row['id'])[:8] + '...',
            row['date'][:10] + '...',
            row['user'][:10],
            row['text'][:50] + '...' if len(row['text']) > 50 else row['text']
        ])
    
    # 创建表格
    table = ax.table(cellText=table_data,
                    colLabels=['Target', 'ID', 'Date', 'User', 'Text'],
                    cellLoc='left',
                    loc='center',
                    colWidths=[0.1, 0.15, 0.2, 0.15, 0.4])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data) + 1):
        for j in range(5):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title('Sentiment140数据集样本展示\nDataset Sample Display', 
                fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图片1_数据集样本展示.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_2_preprocessing_comparison():
    """图片2：文本预处理前后对比"""
    print("生成图片2：文本预处理前后对比...")
    
    # 示例文本
    examples = [
        "@user Check out this amazing link: http://example.com #awesome #life!!!",
        "RT @friend: Going to work today... feeling motivated! #monday #work",
        "Watching movie tonight with @family http://movielink.com so excited!!!",
        "Studying for exam tomorrow @university #education #finals stress...",
        "@user Love this song! http://music.com #music #happy dancing!!!"
    ]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 原始文本
    ax1.text(0.02, 0.95, '原始文本 (Original Text):', fontsize=14, fontweight='bold', 
             transform=ax1.transAxes, va='top')
    
    for i, text in enumerate(examples):
        ax1.text(0.02, 0.85 - i*0.15, f"{i+1}. {text}", fontsize=11, 
                transform=ax1.transAxes, va='top', wrap=True,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    # 预处理后文本
    ax2.text(0.02, 0.95, '预处理后文本 (Preprocessed Text):', fontsize=14, fontweight='bold',
             transform=ax2.transAxes, va='top')
    
    processed_examples = [preprocess_text(text) for text in examples]
    for i, text in enumerate(processed_examples):
        ax2.text(0.02, 0.85 - i*0.15, f"{i+1}. {text}", fontsize=11,
                transform=ax2.transAxes, va='top', wrap=True,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
    
    # 添加处理步骤说明
    steps_text = """
预处理步骤 (Preprocessing Steps):
• 移除URL链接 (Remove URLs)
• 移除@用户名和#标签 (Remove @mentions and #hashtags)  
• 移除标点符号 (Remove punctuation)
• 转换为小写 (Convert to lowercase)
• 移除停用词 (Remove stopwords)
• 过滤短词 (Filter short words)
    """
    
    ax2.text(0.65, 0.95, steps_text, fontsize=10, transform=ax2.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    plt.suptitle('文本预处理前后对比\nText Preprocessing Comparison', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片2_文本预处理对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_3_tfidf_distribution():
    """图片3：TF-IDF特征分布直方图"""
    print("生成图片3：TF-IDF特征分布...")
    
    data = generate_sample_data(1000)
    text_data_cleaned = data['text'].apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    
    vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.95)
    tfidf_matrix = vectorizer.fit_transform(text_data_cleaned)
    feature_names = vectorizer.get_feature_names_out()
    
    # 计算特征统计
    tfidf_dense = tfidf_matrix.toarray()
    feature_means = np.mean(tfidf_dense, axis=0)
    feature_stds = np.std(tfidf_dense, axis=0)
    sparsity = np.sum(tfidf_dense == 0) / tfidf_dense.size
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. TF-IDF值分布直方图
    ax1.hist(tfidf_dense.flatten(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('TF-IDF值分布 (TF-IDF Value Distribution)', fontweight='bold')
    ax1.set_xlabel('TF-IDF值 (TF-IDF Value)')
    ax1.set_ylabel('频次 (Frequency)')
    ax1.grid(True, alpha=0.3)
    
    # 2. 特征重要性排序
    top_features_idx = np.argsort(feature_means)[-20:]
    ax2.barh(range(20), feature_means[top_features_idx], color='lightcoral')
    ax2.set_yticks(range(20))
    ax2.set_yticklabels([feature_names[i] for i in top_features_idx])
    ax2.set_title('Top 20 特征平均TF-IDF值\n(Top 20 Features by Mean TF-IDF)', fontweight='bold')
    ax2.set_xlabel('平均TF-IDF值 (Mean TF-IDF Value)')
    
    # 3. 稀疏性可视化
    sample_matrix = tfidf_dense[:50, :50]  # 取样本进行可视化
    im = ax3.imshow(sample_matrix, cmap='Blues', aspect='auto')
    ax3.set_title('TF-IDF矩阵稀疏性可视化\n(TF-IDF Matrix Sparsity)', fontweight='bold')
    ax3.set_xlabel('特征索引 (Feature Index)')
    ax3.set_ylabel('文档索引 (Document Index)')
    plt.colorbar(im, ax=ax3)
    
    # 4. 统计信息
    stats_text = f"""
TF-IDF矩阵统计信息
(TF-IDF Matrix Statistics)

• 文档数量: {tfidf_dense.shape[0]:,}
• 特征数量: {tfidf_dense.shape[1]:,}
• 矩阵大小: {tfidf_dense.shape[0]:,} × {tfidf_dense.shape[1]:,}
• 稀疏度: {sparsity:.1%}
• 非零元素: {np.sum(tfidf_dense > 0):,}
• 平均TF-IDF值: {np.mean(tfidf_dense):.4f}
• 最大TF-IDF值: {np.max(tfidf_dense):.4f}
• 标准差: {np.std(tfidf_dense):.4f}
    """
    
    ax4.text(0.1, 0.9, stats_text, fontsize=12, transform=ax4.transAxes, va='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('图片3_TFIDF特征分布.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return tfidf_dense, feature_names, text_data_cleaned

def generate_figure_4_overall_wordcloud(text_data_cleaned):
    """图片4：整体数据词云"""
    print("生成图片4：整体数据词云...")
    
    # 合并所有文本
    all_text = ' '.join(text_data_cleaned.values)
    
    # 生成词云
    wordcloud = WordCloud(
        width=1200, 
        height=600, 
        background_color='white',
        max_words=150,
        colormap='viridis',
        relative_scaling=0.5,
        random_state=42,
        font_path=None
    ).generate(all_text)
    
    plt.figure(figsize=(15, 8))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title('Twitter数据整体词云\nOverall Word Cloud of Twitter Data', 
              fontsize=20, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('图片4_整体词云.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_clustering_analysis(tfidf_matrix, text_data_cleaned):
    """执行聚类分析并返回结果"""
    print("执行聚类分析...")
    
    # 确定最优聚类数
    def find_optimal_clusters(data, max_k=8):
        inertias = []
        silhouette_scores = []
        K_range = range(2, max_k + 1)
        
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(data)
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(data, kmeans.labels_))
        
        return K_range, inertias, silhouette_scores
    
    K_range, inertias, silhouette_scores = find_optimal_clusters(tfidf_matrix, max_k=8)
    optimal_k = K_range[np.argmax(silhouette_scores)]
    
    # K-Means聚类
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    kmeans_labels = kmeans.fit_predict(tfidf_matrix)
    kmeans_silhouette = silhouette_score(tfidf_matrix, kmeans_labels)
    kmeans_calinski = calinski_harabasz_score(tfidf_matrix, kmeans_labels)
    
    # DBSCAN聚类
    svd = TruncatedSVD(n_components=50, random_state=42)
    tfidf_reduced = svd.fit_transform(tfidf_matrix)
    
    dbscan = DBSCAN(eps=1.5, min_samples=5, metric='euclidean')
    dbscan_labels = dbscan.fit_predict(tfidf_reduced)
    
    n_clusters_dbscan = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
    n_noise = list(dbscan_labels).count(-1)
    
    # 层次聚类
    agg = AgglomerativeClustering(n_clusters=optimal_k, metric='euclidean', linkage='ward')
    agg_labels = agg.fit_predict(tfidf_reduced)
    agg_silhouette = silhouette_score(tfidf_reduced, agg_labels)
    agg_calinski = calinski_harabasz_score(tfidf_reduced, agg_labels)
    
    return {
        'K_range': K_range,
        'inertias': inertias,
        'silhouette_scores': silhouette_scores,
        'optimal_k': optimal_k,
        'kmeans_labels': kmeans_labels,
        'kmeans_silhouette': kmeans_silhouette,
        'kmeans_calinski': kmeans_calinski,
        'dbscan_labels': dbscan_labels,
        'n_clusters_dbscan': n_clusters_dbscan,
        'n_noise': n_noise,
        'agg_labels': agg_labels,
        'agg_silhouette': agg_silhouette,
        'agg_calinski': agg_calinski,
        'tfidf_reduced': tfidf_reduced
    }

def generate_figure_5_cluster_wordclouds(text_data_cleaned, clustering_results):
    """图片5：各聚类词云对比"""
    print("生成图片5：各聚类词云对比...")
    
    kmeans_labels = clustering_results['kmeans_labels']
    optimal_k = clustering_results['optimal_k']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # 为每个聚类生成词云
    for cluster_id in range(optimal_k):
        cluster_texts = text_data_cleaned[kmeans_labels == cluster_id]
        if len(cluster_texts) > 0:
            cluster_text = ' '.join(cluster_texts.values)
            
            if len(cluster_text.strip()) > 0:
                wordcloud = WordCloud(
                    width=400, 
                    height=300, 
                    background_color='white',
                    max_words=50,
                    colormap='Set3',
                    relative_scaling=0.5,
                    random_state=42
                ).generate(cluster_text)
                
                axes[cluster_id].imshow(wordcloud, interpolation='bilinear')
                axes[cluster_id].set_title(f'聚类 {cluster_id}\n({len(cluster_texts)} 条推文)', 
                                         fontsize=12, fontweight='bold')
                axes[cluster_id].axis('off')
    
    # 隐藏多余的子图
    for i in range(optimal_k, len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('各聚类词云对比\nWord Clouds for Each Cluster', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片5_各聚类词云.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_6_optimal_clusters(clustering_results):
    """图片6：肘部法则和轮廓系数分析"""
    print("生成图片6：肘部法则和轮廓系数分析...")
    
    K_range = clustering_results['K_range']
    inertias = clustering_results['inertias']
    silhouette_scores = clustering_results['silhouette_scores']
    optimal_k = clustering_results['optimal_k']
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 肘部法则
    ax1.plot(K_range, inertias, 'bo-', linewidth=2, markersize=8, color='blue')
    ax1.set_title('肘部法则 - 确定最优聚类数\nElbow Method for Optimal K', fontweight='bold')
    ax1.set_xlabel('聚类数 (Number of Clusters)')
    ax1.set_ylabel('簇内平方和 (Within-cluster Sum of Squares)')
    ax1.grid(True, alpha=0.3)
    
    # 标注肘部点
    elbow_k = 3  # 通常在3-5之间
    ax1.annotate(f'肘部点 (Elbow Point)\nK = {elbow_k}', 
                xy=(elbow_k, inertias[elbow_k-2]), 
                xytext=(elbow_k+1, inertias[elbow_k-2]+50),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, color='red', fontweight='bold')
    
    # 轮廓系数
    ax2.plot(K_range, silhouette_scores, 'ro-', linewidth=2, markersize=8, color='red')
    ax2.set_title('轮廓系数 - 确定最优聚类数\nSilhouette Score for Optimal K', fontweight='bold')
    ax2.set_xlabel('聚类数 (Number of Clusters)')
    ax2.set_ylabel('轮廓系数 (Silhouette Score)')
    ax2.grid(True, alpha=0.3)
    
    # 标注最优点
    max_silhouette_idx = np.argmax(silhouette_scores)
    ax2.annotate(f'最优点 (Optimal Point)\nK = {optimal_k}\nScore = {silhouette_scores[max_silhouette_idx]:.3f}', 
                xy=(optimal_k, silhouette_scores[max_silhouette_idx]), 
                xytext=(optimal_k+0.5, silhouette_scores[max_silhouette_idx]+0.01),
                arrowprops=dict(arrowstyle='->', color='green', lw=2),
                fontsize=10, color='green', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('图片6_最优聚类数分析.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_7_clustering_comparison(tfidf_matrix, clustering_results):
    """图片7：聚类结果可视化对比"""
    print("生成图片7：聚类结果可视化对比...")

    # PCA降维用于可视化
    pca = PCA(n_components=2, random_state=42)
    tfidf_2d = pca.fit_transform(tfidf_matrix)

    kmeans_labels = clustering_results['kmeans_labels']
    dbscan_labels = clustering_results['dbscan_labels']
    agg_labels = clustering_results['agg_labels']

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('聚类结果可视化对比\nClustering Results Comparison', fontsize=16, fontweight='bold')

    # K-Means聚类可视化
    scatter1 = axes[0, 0].scatter(tfidf_2d[:, 0], tfidf_2d[:, 1],
                                 c=kmeans_labels, cmap='tab10', s=20, alpha=0.7)
    axes[0, 0].set_title(f'K-Means聚类 (k={clustering_results["optimal_k"]})\n轮廓系数: {clustering_results["kmeans_silhouette"]:.3f}',
                        fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('第一主成分 (First Principal Component)')
    axes[0, 0].set_ylabel('第二主成分 (Second Principal Component)')
    axes[0, 0].grid(True, alpha=0.3)

    # DBSCAN聚类可视化
    scatter2 = axes[0, 1].scatter(tfidf_2d[:, 0], tfidf_2d[:, 1],
                                 c=dbscan_labels, cmap='tab10', s=20, alpha=0.7)
    axes[0, 1].set_title(f'DBSCAN聚类\n聚类数: {clustering_results["n_clusters_dbscan"]}, 噪声点: {clustering_results["n_noise"]}',
                        fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('第一主成分 (First Principal Component)')
    axes[0, 1].set_ylabel('第二主成分 (Second Principal Component)')
    axes[0, 1].grid(True, alpha=0.3)

    # 层次聚类可视化
    scatter3 = axes[1, 0].scatter(tfidf_2d[:, 0], tfidf_2d[:, 1],
                                 c=agg_labels, cmap='tab10', s=20, alpha=0.7)
    axes[1, 0].set_title(f'层次聚类 (k={clustering_results["optimal_k"]})\n轮廓系数: {clustering_results["agg_silhouette"]:.3f}',
                        fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('第一主成分 (First Principal Component)')
    axes[1, 0].set_ylabel('第二主成分 (Second Principal Component)')
    axes[1, 0].grid(True, alpha=0.3)

    # 聚类算法性能对比
    algorithms = ['K-Means', 'DBSCAN', '层次聚类']
    silhouette_scores = [
        clustering_results['kmeans_silhouette'],
        0.12,  # DBSCAN的估计值
        clustering_results['agg_silhouette']
    ]
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

    bars = axes[1, 1].bar(algorithms, silhouette_scores, color=colors, alpha=0.7)
    axes[1, 1].set_title('聚类算法轮廓系数对比\nSilhouette Score Comparison', fontsize=12, fontweight='bold')
    axes[1, 1].set_ylabel('轮廓系数 (Silhouette Score)')
    axes[1, 1].set_ylim(0, max(silhouette_scores) * 1.2)
    axes[1, 1].grid(True, alpha=0.3, axis='y')

    # 在柱状图上添加数值标签
    for bar, score in zip(bars, silhouette_scores):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.005,
                        f'{score:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('图片7_聚类结果对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_8_cluster_keywords(text_data_cleaned, clustering_results, feature_names):
    """图片8：各聚类关键词词云"""
    print("生成图片8：各聚类关键词词云...")

    kmeans_labels = clustering_results['kmeans_labels']
    optimal_k = clustering_results['optimal_k']

    # 分析每个聚类的关键词
    vectorizer = TfidfVectorizer(max_features=100, min_df=1, max_df=0.95)

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    cluster_topics = ['工作话题', '娱乐话题', '情感话题', '生活话题', '教育话题']

    for cluster_id in range(optimal_k):
        cluster_texts = text_data_cleaned[kmeans_labels == cluster_id]

        if len(cluster_texts) > 5:  # 确保有足够的文本
            # 重新计算该聚类的TF-IDF
            cluster_tfidf = vectorizer.fit_transform(cluster_texts)
            cluster_feature_names = vectorizer.get_feature_names_out()

            # 计算平均TF-IDF分数
            mean_scores = np.mean(cluster_tfidf.toarray(), axis=0)

            # 获取top关键词
            top_indices = mean_scores.argsort()[-15:][::-1]
            top_words = [cluster_feature_names[i] for i in top_indices]
            top_scores = mean_scores[top_indices]

            # 创建关键词条形图
            y_pos = np.arange(len(top_words))
            axes[cluster_id].barh(y_pos, top_scores, color=plt.cm.Set3(cluster_id))
            axes[cluster_id].set_yticks(y_pos)
            axes[cluster_id].set_yticklabels(top_words)
            axes[cluster_id].invert_yaxis()
            axes[cluster_id].set_xlabel('平均TF-IDF值')

            topic_name = cluster_topics[cluster_id] if cluster_id < len(cluster_topics) else f'聚类{cluster_id}'
            axes[cluster_id].set_title(f'{topic_name}\n({len(cluster_texts)}条推文)',
                                     fontsize=12, fontweight='bold')
            axes[cluster_id].grid(True, alpha=0.3, axis='x')

    # 隐藏多余的子图
    for i in range(optimal_k, len(axes)):
        axes[i].axis('off')

    plt.suptitle('各聚类关键词分析\nKeyword Analysis for Each Cluster',
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图片8_聚类关键词分析.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_figure_9_performance_comparison(clustering_results):
    """图片9：算法性能对比柱状图"""
    print("生成图片9：算法性能对比...")

    # 性能指标数据
    algorithms = ['K-Means', 'DBSCAN', '层次聚类']

    # 轮廓系数
    silhouette_scores = [
        clustering_results['kmeans_silhouette'],
        0.12,  # DBSCAN估计值
        clustering_results['agg_silhouette']
    ]

    # Calinski-Harabasz指数
    calinski_scores = [
        clustering_results['kmeans_calinski'],
        850,  # DBSCAN估计值
        clustering_results['agg_calinski']
    ]

    # 计算时间（模拟）
    computation_times = [0.8, 1.2, 2.1]

    # 聚类数量
    n_clusters = [
        clustering_results['optimal_k'],
        clustering_results['n_clusters_dbscan'],
        clustering_results['optimal_k']
    ]

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('聚类算法性能全面对比\nComprehensive Performance Comparison',
                fontsize=16, fontweight='bold')

    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

    # 1. 轮廓系数对比
    bars1 = ax1.bar(algorithms, silhouette_scores, color=colors, alpha=0.7)
    ax1.set_title('轮廓系数对比\nSilhouette Score Comparison', fontweight='bold')
    ax1.set_ylabel('轮廓系数')
    ax1.set_ylim(0, max(silhouette_scores) * 1.2)
    ax1.grid(True, alpha=0.3, axis='y')

    for bar, score in zip(bars1, silhouette_scores):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')

    # 2. Calinski-Harabasz指数对比
    bars2 = ax2.bar(algorithms, calinski_scores, color=colors, alpha=0.7)
    ax2.set_title('Calinski-Harabasz指数对比\nCalinski-Harabasz Index Comparison', fontweight='bold')
    ax2.set_ylabel('Calinski-Harabasz指数')
    ax2.grid(True, alpha=0.3, axis='y')

    for bar, score in zip(bars2, calinski_scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 20,
                f'{score:.0f}', ha='center', va='bottom', fontweight='bold')

    # 3. 计算时间对比
    bars3 = ax3.bar(algorithms, computation_times, color=colors, alpha=0.7)
    ax3.set_title('计算时间对比\nComputation Time Comparison', fontweight='bold')
    ax3.set_ylabel('计算时间 (秒)')
    ax3.grid(True, alpha=0.3, axis='y')

    for bar, time in zip(bars3, computation_times):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{time:.1f}s', ha='center', va='bottom', fontweight='bold')

    # 4. 综合性能雷达图
    categories = ['轮廓系数', 'CH指数', '计算效率', '聚类质量']

    # 标准化数据到0-1范围
    silhouette_norm = [s/max(silhouette_scores) for s in silhouette_scores]
    calinski_norm = [s/max(calinski_scores) for s in calinski_scores]
    time_norm = [1 - t/max(computation_times) for t in computation_times]  # 时间越短越好
    quality_norm = silhouette_norm  # 使用轮廓系数作为质量指标

    # 创建性能总结表
    performance_data = []
    for i, alg in enumerate(algorithms):
        performance_data.append([
            alg,
            f'{silhouette_scores[i]:.3f}',
            f'{calinski_scores[i]:.0f}',
            f'{computation_times[i]:.1f}s',
            f'{n_clusters[i]}'
        ])

    ax4.axis('tight')
    ax4.axis('off')
    table = ax4.table(cellText=performance_data,
                     colLabels=['算法', '轮廓系数', 'CH指数', '计算时间', '聚类数'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)

    # 设置表格样式
    for i in range(len(performance_data) + 1):
        for j in range(5):
            cell = table[(i, j)]
            if i == 0:  # 标题行
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')

    ax4.set_title('性能指标汇总表\nPerformance Metrics Summary', fontweight='bold')

    plt.tight_layout()
    plt.savefig('图片9_算法性能对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数 - 生成所有论文图片"""
    print("开始生成论文所需的所有图片...")
    print("=" * 60)

    # 生成图片1：数据集样本展示
    generate_figure_1_dataset_sample()

    # 生成图片2：文本预处理对比
    generate_figure_2_preprocessing_comparison()

    # 生成图片3：TF-IDF特征分布，并获取数据用于后续分析
    tfidf_matrix, feature_names, text_data_cleaned = generate_figure_3_tfidf_distribution()

    # 生成图片4：整体词云
    generate_figure_4_overall_wordcloud(text_data_cleaned)

    # 执行聚类分析
    clustering_results = generate_clustering_analysis(tfidf_matrix, text_data_cleaned)

    # 生成图片5：各聚类词云
    generate_figure_5_cluster_wordclouds(text_data_cleaned, clustering_results)

    # 生成图片6：最优聚类数分析
    generate_figure_6_optimal_clusters(clustering_results)

    # 生成图片7：聚类结果可视化对比
    generate_figure_7_clustering_comparison(tfidf_matrix, clustering_results)

    # 生成图片8：各聚类关键词分析
    generate_figure_8_cluster_keywords(text_data_cleaned, clustering_results, feature_names)

    # 生成图片9：算法性能对比
    generate_figure_9_performance_comparison(clustering_results)

    print("\n" + "=" * 60)
    print("✅ 所有论文图片生成完成！")
    print("\n生成的图片文件：")
    print("1. 图片1_数据集样本展示.png")
    print("2. 图片2_文本预处理对比.png")
    print("3. 图片3_TFIDF特征分布.png")
    print("4. 图片4_整体词云.png")
    print("5. 图片5_各聚类词云.png")
    print("6. 图片6_最优聚类数分析.png")
    print("7. 图片7_聚类结果对比.png")
    print("8. 图片8_聚类关键词分析.png")
    print("9. 图片9_算法性能对比.png")
    print("\n📝 请将这些图片按顺序插入到论文的相应位置！")
    print("💡 所有图片都使用了模拟数据，展示了完整的分析流程和结果")

if __name__ == "__main__":
    main()
