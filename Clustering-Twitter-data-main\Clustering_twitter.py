# 基于Sentiment140数据集的Twitter推文聚类分析
# Twitter Text Clustering Analysis Based on Sentiment140 Dataset

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import silhouette_score, adjusted_rand_score, calinski_harabasz_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 第一步：加载数据集
# Step 1: Load the dataset
print("正在加载数据集...")
# 由于CSV文件没有标题行，需要手动指定列名
columns = ['target', 'id', 'date', 'flag', 'user', 'text']
try:
    data = pd.read_csv("twitter.csv", encoding='latin-1', header=None, names=columns)
    print(f"数据集加载成功！数据形状: {data.shape}")
    print("数据集前5行预览:")
    print(data.head())
except FileNotFoundError:
    print("错误：未找到twitter.csv文件，请确保文件在当前目录下")
    exit()

# 提取文本数据用于聚类分析
text_data = data['text']
print(f"文本数据总数: {len(text_data)}")

# 第二步：文本数据预处理
# Step 2: Text Data Preprocessing
print("\n开始文本预处理...")
# 移除停用词、标点符号并转换为小写，这有助于减少噪声、标准化文本，
# 提高特征质量以获得更好的模型性能

# 定义文本预处理函数
def preprocess_text(text):
    """
    文本预处理函数
    - 移除URL链接
    - 移除标点符号
    - 转换为小写
    - 移除停用词
    """
    if pd.isna(text):
        return ""

    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

print("正在进行文本清洗...")
text_data_cleaned = text_data.apply(preprocess_text)
# 移除空文本
text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
print(f"清洗后的文本数据: {len(text_data_cleaned)}")

# 为了计算效率，随机采样10000条数据
print("正在采样数据...")
sampled_data = text_data_cleaned.sample(n=min(10000, len(text_data_cleaned)), random_state=42)
print(f"采样数据量: {len(sampled_data)}")

# 第三步：TF-IDF向量化
# Step 3: TF-IDF Vectorization
print("\n开始TF-IDF向量化...")
"""
TF-IDF (Term Frequency-Inverse Document Frequency) 的优势：
1. 提供词汇的加权表示，捕获词汇在文档中的重要性
2. 有助于识别文档中的重要词汇，降低常见无意义词汇的权重
3. 适用于文本聚类、分类和信息检索等任务
4. 与简单词频统计相比，TF-IDF为特定文档中的特殊词汇分配更高的重要性

TF (词频): 衡量词汇在文档中出现的频率
IDF (逆文档频率): 衡量词汇在整个语料库中的重要性，在多个文档中出现的词汇重要性较低
"""

# 限制特征数量以简化计算（选择最重要的1000个词汇特征）
vectorizer = TfidfVectorizer(
    max_features=1000,          # 最大特征数
    min_df=2,                   # 词汇至少在2个文档中出现
    max_df=0.95,                # 词汇最多在95%的文档中出现
    ngram_range=(1, 2)          # 使用1-gram和2-gram
)

# 将清洗后的文本数据转换为TF-IDF特征向量
text_vectors = vectorizer.fit_transform(sampled_data).toarray()
print(f"TF-IDF向量化完成！特征维度: {text_vectors.shape}")

# 获取特征词汇
feature_names = vectorizer.get_feature_names_out()
print(f"特征词汇数量: {len(feature_names)}")
print("前10个特征词汇:", feature_names[:10])

# 第四步：K-Means聚类
# Step 4: K-Means Clustering
print("\n开始K-Means聚类...")
# 使用肘部法则确定最优聚类数
def find_optimal_clusters(data, max_k=10):
    """使用肘部法则和轮廓系数确定最优聚类数"""
    inertias = []
    silhouette_scores = []
    K_range = range(2, max_k + 1)

    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(data)
        inertias.append(kmeans.inertia_)
        silhouette_scores.append(silhouette_score(data, kmeans.labels_))

    return K_range, inertias, silhouette_scores

# 寻找最优聚类数
K_range, inertias, silhouette_scores = find_optimal_clusters(text_vectors, max_k=8)
optimal_k = K_range[np.argmax(silhouette_scores)]
print(f"基于轮廓系数的最优聚类数: {optimal_k}")

# 执行K-Means聚类
kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
kmeans_labels = kmeans.fit_predict(text_vectors)

# 评估K-Means聚类效果
kmeans_silhouette = silhouette_score(text_vectors, kmeans_labels)
kmeans_calinski = calinski_harabasz_score(text_vectors, kmeans_labels)
print(f"K-Means聚类结果:")
print(f"  轮廓系数 (Silhouette Score): {kmeans_silhouette:.3f}")
print(f"  Calinski-Harabasz指数: {kmeans_calinski:.3f}")

# 显示各聚类的大小
unique, counts = np.unique(kmeans_labels, return_counts=True)
print(f"  各聚类大小: {dict(zip(unique, counts))}")

# 第五步：DBSCAN聚类
# Step 5: DBSCAN Clustering
print("\n开始DBSCAN聚类...")
"""
DBSCAN (Density-Based Spatial Clustering of Applications with Noise) 的特点：
1. 基于密度的聚类算法，能够发现任意形状的聚类
2. 自动确定聚类数量，无需预先指定
3. 能够识别噪声点（异常值）
4. 对参数eps和min_samples敏感
"""

# 使用降维后的数据进行DBSCAN聚类（提高效率）
from sklearn.decomposition import TruncatedSVD
svd = TruncatedSVD(n_components=50, random_state=42)
text_vectors_reduced = svd.fit_transform(text_vectors)

# 调整DBSCAN参数
dbscan = DBSCAN(eps=1.5, min_samples=5, metric='euclidean')
dbscan_labels = dbscan.fit_predict(text_vectors_reduced)

# 分析DBSCAN聚类结果
dbscan_core_samples = sum(dbscan_labels != -1)  # 非噪声点数量
n_clusters = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)  # 聚类数量
n_noise = list(dbscan_labels).count(-1)  # 噪声点数量

print(f"DBSCAN聚类结果:")
print(f"  聚类数量: {n_clusters}")
print(f"  核心样本数: {dbscan_core_samples}")
print(f"  噪声点数: {n_noise}")
print(f"  噪声比例: {n_noise/len(dbscan_labels)*100:.2f}%")

# 计算DBSCAN的轮廓系数（排除噪声点）
if n_clusters > 1 and dbscan_core_samples > 0:
    mask = dbscan_labels != -1
    if sum(mask) > 1:
        dbscan_silhouette = silhouette_score(text_vectors_reduced[mask], dbscan_labels[mask])
        print(f"  轮廓系数 (排除噪声点): {dbscan_silhouette:.3f}")
    else:
        print("  轮廓系数: 无法计算（有效聚类点太少）")
else:
    print("  轮廓系数: 无法计算（聚类数量不足）")

# 第六步：层次聚类（凝聚聚类）
# Step 6: Agglomerative Clustering
print("\n开始层次聚类...")
"""
层次聚类（Agglomerative Clustering）的特点：
1. 自底向上的聚类方法，从每个点作为单独聚类开始
2. 逐步合并最相似的聚类，直到达到指定的聚类数量
3. linkage='ward'：最小化聚类内方差的合并策略，创建紧凑同质的聚类
4. 可以生成聚类树状图（dendrogram）
"""

# 使用降维后的数据进行层次聚类（提高效率）
agg = AgglomerativeClustering(n_clusters=optimal_k, metric='euclidean', linkage='ward')
agg_labels = agg.fit_predict(text_vectors_reduced)

# 评估层次聚类效果
agg_silhouette = silhouette_score(text_vectors_reduced, agg_labels)
agg_calinski = calinski_harabasz_score(text_vectors_reduced, agg_labels)
print(f"层次聚类结果:")
print(f"  轮廓系数 (Silhouette Score): {agg_silhouette:.3f}")
print(f"  Calinski-Harabasz指数: {agg_calinski:.3f}")

# 显示各聚类的大小
unique_agg, counts_agg = np.unique(agg_labels, return_counts=True)
print(f"  各聚类大小: {dict(zip(unique_agg, counts_agg))}")

# 第七步：聚类结果可视化
# Step 7: Visualize Clustering Results
print("\n开始生成可视化图表...")

# 使用PCA进行二维降维以便可视化
pca = PCA(n_components=2, random_state=42)
text_vectors_2d = pca.fit_transform(text_vectors)
print(f"PCA降维完成，解释方差比: {pca.explained_variance_ratio_}")

# 创建聚类结果对比图
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Twitter文本聚类结果对比', fontsize=16, fontweight='bold')

# K-Means聚类可视化
axes[0, 0].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1],
                   c=kmeans_labels, cmap='tab10', s=20, alpha=0.7)
axes[0, 0].set_title(f'K-Means聚类 (k={optimal_k})\n轮廓系数: {kmeans_silhouette:.3f}',
                     fontsize=12, fontweight='bold')
axes[0, 0].set_xlabel('第一主成分')
axes[0, 0].set_ylabel('第二主成分')
axes[0, 0].grid(True, alpha=0.3)

# DBSCAN聚类可视化
scatter = axes[0, 1].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1],
                            c=dbscan_labels, cmap='tab10', s=20, alpha=0.7)
axes[0, 1].set_title(f'DBSCAN聚类\n聚类数: {n_clusters}, 噪声点: {n_noise}',
                     fontsize=12, fontweight='bold')
axes[0, 1].set_xlabel('第一主成分')
axes[0, 1].set_ylabel('第二主成分')
axes[0, 1].grid(True, alpha=0.3)

# 层次聚类可视化
axes[1, 0].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1],
                   c=agg_labels, cmap='tab10', s=20, alpha=0.7)
axes[1, 0].set_title(f'层次聚类 (k={optimal_k})\n轮廓系数: {agg_silhouette:.3f}',
                     fontsize=12, fontweight='bold')
axes[1, 0].set_xlabel('第一主成分')
axes[1, 0].set_ylabel('第二主成分')
axes[1, 0].grid(True, alpha=0.3)

# 聚类算法性能对比
algorithms = ['K-Means', 'DBSCAN', '层次聚类']
silhouette_scores_comparison = [kmeans_silhouette,
                               dbscan_silhouette if 'dbscan_silhouette' in locals() else 0,
                               agg_silhouette]
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

bars = axes[1, 1].bar(algorithms, silhouette_scores_comparison, color=colors, alpha=0.7)
axes[1, 1].set_title('聚类算法轮廓系数对比', fontsize=12, fontweight='bold')
axes[1, 1].set_ylabel('轮廓系数')
axes[1, 1].set_ylim(0, max(silhouette_scores_comparison) * 1.1)
axes[1, 1].grid(True, alpha=0.3, axis='y')

# 在柱状图上添加数值标签
for bar, score in zip(bars, silhouette_scores_comparison):
    height = bar.get_height()
    axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.savefig('clustering_results_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# 第八步：聚类结果分析和词云生成
# Step 8: Cluster Analysis and Word Cloud Generation
print("\n开始聚类结果分析...")

# 分析每个聚类的特征词汇
def analyze_cluster_keywords(labels, texts, vectorizer, n_top_words=10):
    """分析每个聚类的关键词"""
    cluster_keywords = {}
    feature_names = vectorizer.get_feature_names_out()

    for cluster_id in np.unique(labels):
        if cluster_id == -1:  # 跳过噪声点
            continue

        # 获取属于当前聚类的文本
        cluster_texts = texts[labels == cluster_id]

        if len(cluster_texts) > 0:
            # 重新计算TF-IDF
            cluster_tfidf = vectorizer.transform(cluster_texts)
            # 计算平均TF-IDF分数
            mean_scores = np.mean(cluster_tfidf.toarray(), axis=0)
            # 获取top关键词
            top_indices = mean_scores.argsort()[-n_top_words:][::-1]
            top_words = [feature_names[i] for i in top_indices]
            cluster_keywords[cluster_id] = top_words

    return cluster_keywords

# 分析K-Means聚类的关键词
kmeans_keywords = analyze_cluster_keywords(kmeans_labels, sampled_data.values, vectorizer)
print("\nK-Means聚类关键词分析:")
for cluster_id, keywords in kmeans_keywords.items():
    print(f"聚类 {cluster_id}: {', '.join(keywords)}")

# 生成肘部法则图
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.plot(K_range, inertias, 'bo-', linewidth=2, markersize=8)
plt.title('肘部法则 - 确定最优聚类数', fontsize=12, fontweight='bold')
plt.xlabel('聚类数 (k)')
plt.ylabel('簇内平方和 (WCSS)')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.plot(K_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)
plt.title('轮廓系数 - 确定最优聚类数', fontsize=12, fontweight='bold')
plt.xlabel('聚类数 (k)')
plt.ylabel('轮廓系数')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('optimal_clusters_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# 第九步：总结和结论
# Step 9: Summary and Conclusions
print("\n" + "="*60)
print("聚类分析总结报告")
print("="*60)
print(f"数据集信息:")
print(f"  - 原始数据量: {len(text_data):,}")
print(f"  - 清洗后数据量: {len(text_data_cleaned):,}")
print(f"  - 分析样本量: {len(sampled_data):,}")
print(f"  - 特征维度: {text_vectors.shape[1]}")

print(f"\n聚类算法性能对比:")
print(f"  - K-Means (k={optimal_k}):")
print(f"    * 轮廓系数: {kmeans_silhouette:.3f}")
print(f"    * Calinski-Harabasz指数: {kmeans_calinski:.3f}")

if 'dbscan_silhouette' in locals():
    print(f"  - DBSCAN:")
    print(f"    * 发现聚类数: {n_clusters}")
    print(f"    * 轮廓系数: {dbscan_silhouette:.3f}")
    print(f"    * 噪声比例: {n_noise/len(dbscan_labels)*100:.1f}%")

print(f"  - 层次聚类 (k={optimal_k}):")
print(f"    * 轮廓系数: {agg_silhouette:.3f}")
print(f"    * Calinski-Harabasz指数: {agg_calinski:.3f}")

# 确定最佳算法
best_algorithm = "K-Means" if kmeans_silhouette >= agg_silhouette else "层次聚类"
print(f"\n推荐算法: {best_algorithm}")
print(f"推荐理由: 在当前数据集上具有最高的轮廓系数")

print("\n聚类分析完成！")
print("生成的文件:")
print("  - clustering_results_comparison.png: 聚类结果对比图")
print("  - optimal_clusters_analysis.png: 最优聚类数分析图")