# 测试脚本 - 无需真实数据集
# Test script - No real dataset required

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA, TruncatedSVD
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def generate_sample_data(n_samples=1000):
    """生成模拟Twitter数据用于测试"""
    print("生成模拟数据用于测试...")
    
    # 模拟不同主题的推文
    topics = {
        'work': [
            'going to work today', 'office meeting scheduled', 'project deadline approaching',
            'working late tonight', 'job interview tomorrow', 'busy day at office',
            'team collaboration project', 'work from home today', 'conference call meeting'
        ],
        'entertainment': [
            'watching movie tonight', 'listening to music', 'playing video games',
            'concert was amazing', 'new album released', 'favorite tv show',
            'weekend party plans', 'dancing all night', 'comedy show funny'
        ],
        'emotion': [
            'feeling happy today', 'love my family', 'sad about news',
            'excited for vacation', 'grateful for friends', 'missing home',
            'proud of achievement', 'worried about future', 'feeling blessed'
        ],
        'daily_life': [
            'cooking dinner tonight', 'grocery shopping done', 'cleaning house today',
            'walking in park', 'morning coffee time', 'reading good book',
            'family dinner together', 'sleeping early tonight', 'beautiful weather outside'
        ],
        'education': [
            'studying for exam', 'learning new skills', 'attending class today',
            'homework assignment due', 'research project interesting', 'library study session',
            'online course helpful', 'graduation ceremony soon', 'academic conference'
        ]
    }
    
    # 生成样本数据
    sample_texts = []
    for _ in range(n_samples):
        topic = np.random.choice(list(topics.keys()))
        base_text = np.random.choice(topics[topic])
        
        # 添加一些随机变化
        variations = [
            f"@user {base_text} #topic",
            f"{base_text} http://example.com",
            f"RT @user: {base_text}",
            f"{base_text}!!!",
            base_text
        ]
        
        sample_texts.append(np.random.choice(variations))
    
    # 创建DataFrame
    data = pd.DataFrame({
        'target': np.random.choice([0, 4], n_samples),
        'id': range(n_samples),
        'date': ['Mon Apr 06 22:19:45 PDT 2009'] * n_samples,
        'flag': ['NO_QUERY'] * n_samples,
        'user': [f'user_{i%100}' for i in range(n_samples)],
        'text': sample_texts
    })
    
    print(f"生成了 {n_samples} 条模拟推文数据")
    return data

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def find_optimal_clusters(data, max_k=8):
    """使用肘部法则和轮廓系数确定最优聚类数"""
    inertias = []
    silhouette_scores = []
    K_range = range(2, max_k + 1)
    
    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(data)
        inertias.append(kmeans.inertia_)
        silhouette_scores.append(silhouette_score(data, kmeans.labels_))
    
    return K_range, inertias, silhouette_scores

def test_clustering_pipeline():
    """测试完整的聚类流程"""
    print("开始测试聚类分析流程...")
    print("=" * 50)
    
    # 1. 生成模拟数据
    data = generate_sample_data(1000)
    text_data = data['text']
    
    # 2. 文本预处理
    print("\n进行文本预处理...")
    text_data_cleaned = text_data.apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    print(f"清洗后的文本数据: {len(text_data_cleaned)}")
    
    # 3. TF-IDF向量化
    print("\n进行TF-IDF向量化...")
    vectorizer = TfidfVectorizer(
        max_features=500,
        min_df=2,
        max_df=0.95,
        ngram_range=(1, 2)
    )
    text_vectors = vectorizer.fit_transform(text_data_cleaned).toarray()
    print(f"TF-IDF向量化完成！特征维度: {text_vectors.shape}")
    
    # 4. 确定最优聚类数
    print("\n确定最优聚类数...")
    K_range, inertias, silhouette_scores = find_optimal_clusters(text_vectors, max_k=6)
    optimal_k = K_range[np.argmax(silhouette_scores)]
    print(f"基于轮廓系数的最优聚类数: {optimal_k}")
    
    # 5. K-Means聚类
    print("\n执行K-Means聚类...")
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    kmeans_labels = kmeans.fit_predict(text_vectors)
    kmeans_silhouette = silhouette_score(text_vectors, kmeans_labels)
    kmeans_calinski = calinski_harabasz_score(text_vectors, kmeans_labels)
    print(f"K-Means聚类结果:")
    print(f"  轮廓系数: {kmeans_silhouette:.3f}")
    print(f"  Calinski-Harabasz指数: {kmeans_calinski:.3f}")
    
    # 6. DBSCAN聚类
    print("\n执行DBSCAN聚类...")
    svd = TruncatedSVD(n_components=50, random_state=42)
    text_vectors_reduced = svd.fit_transform(text_vectors)
    
    dbscan = DBSCAN(eps=1.5, min_samples=5, metric='euclidean')
    dbscan_labels = dbscan.fit_predict(text_vectors_reduced)
    
    n_clusters = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
    n_noise = list(dbscan_labels).count(-1)
    print(f"DBSCAN聚类结果:")
    print(f"  聚类数量: {n_clusters}")
    print(f"  噪声点数: {n_noise}")
    
    # 7. 层次聚类
    print("\n执行层次聚类...")
    agg = AgglomerativeClustering(n_clusters=optimal_k, metric='euclidean', linkage='ward')
    agg_labels = agg.fit_predict(text_vectors_reduced)
    agg_silhouette = silhouette_score(text_vectors_reduced, agg_labels)
    print(f"层次聚类结果:")
    print(f"  轮廓系数: {agg_silhouette:.3f}")
    
    # 8. 可视化结果
    print("\n生成可视化图表...")
    pca = PCA(n_components=2, random_state=42)
    text_vectors_2d = pca.fit_transform(text_vectors)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('聚类结果对比（测试数据）', fontsize=16, fontweight='bold')
    
    # K-Means可视化
    axes[0].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                   c=kmeans_labels, cmap='tab10', s=20, alpha=0.7)
    axes[0].set_title(f'K-Means聚类 (k={optimal_k})')
    axes[0].set_xlabel('第一主成分')
    axes[0].set_ylabel('第二主成分')
    
    # DBSCAN可视化
    axes[1].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                   c=dbscan_labels, cmap='tab10', s=20, alpha=0.7)
    axes[1].set_title(f'DBSCAN聚类 (clusters={n_clusters})')
    axes[1].set_xlabel('第一主成分')
    axes[1].set_ylabel('第二主成分')
    
    # 层次聚类可视化
    axes[2].scatter(text_vectors_2d[:, 0], text_vectors_2d[:, 1], 
                   c=agg_labels, cmap='tab10', s=20, alpha=0.7)
    axes[2].set_title(f'层次聚类 (k={optimal_k})')
    axes[2].set_xlabel('第一主成分')
    axes[2].set_ylabel('第二主成分')
    
    plt.tight_layout()
    plt.savefig('test_clustering_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 9. 生成肘部法则图
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(K_range, inertias, 'bo-', linewidth=2, markersize=8)
    plt.title('肘部法则 - 确定最优聚类数')
    plt.xlabel('聚类数 (k)')
    plt.ylabel('簇内平方和 (WCSS)')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(K_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)
    plt.title('轮廓系数 - 确定最优聚类数')
    plt.xlabel('聚类数 (k)')
    plt.ylabel('轮廓系数')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_optimal_clusters.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n" + "=" * 50)
    print("✓ 测试完成！所有功能正常运行")
    print("生成的测试文件:")
    print("  - test_clustering_results.png")
    print("  - test_optimal_clusters.png")
    print("\n注意：这是使用模拟数据的测试，实际使用时请下载Sentiment140数据集")

if __name__ == "__main__":
    test_clustering_pipeline()
